#!/usr/bin/env python3
"""
Add indicators directly via HTTP requests
"""
import requests
import json

def add_indicators():
    """Add sample indicators via API"""
    base_url = "http://localhost:8000"
    strategy_id = 1
    
    # Sample indicators
    indicators = [
        {
            "indicator_name": "EMA",
            "config": {"periods": [20, 50]},
            "is_enabled": True,
            "display_order": 1
        },
        {
            "indicator_name": "RSI", 
            "config": {"period": 14, "overbought": 70, "oversold": 30},
            "is_enabled": True,
            "display_order": 2
        },
        {
            "indicator_name": "MACD",
            "config": {"fast_period": 12, "slow_period": 26, "signal_period": 9},
            "is_enabled": True,
            "display_order": 3
        },
        {
            "indicator_name": "BOLLINGER_BANDS",
            "config": {"period": 20, "std_dev": 2},
            "is_enabled": True,
            "display_order": 4
        }
    ]
    
    print(f"🧪 Adding indicators to strategy {strategy_id}")
    print("=" * 50)
    
    success_count = 0
    
    for indicator in indicators:
        try:
            url = f"{base_url}/api/v1/indicators/strategies/{strategy_id}/indicators"
            
            print(f"📊 Adding {indicator['indicator_name']}...")
            print(f"   URL: {url}")
            print(f"   Data: {json.dumps(indicator, indent=2)}")
            
            response = requests.post(url, json=indicator, timeout=10)
            
            print(f"   Response: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                print(f"   Result: {json.dumps(result, indent=2)}")
                
                if result.get('success'):
                    print(f"   ✅ Successfully added {indicator['indicator_name']}")
                    success_count += 1
                else:
                    print(f"   ❌ API error: {result}")
            else:
                print(f"   ❌ HTTP {response.status_code}: {response.text}")
                
        except Exception as e:
            print(f"   ❌ Error: {e}")
    
    print(f"\n📊 Summary: {success_count}/{len(indicators)} indicators added")
    
    # Verify
    print(f"\n🔍 Verifying indicators...")
    try:
        verify_url = f"{base_url}/api/v1/indicators/strategies/{strategy_id}/indicators"
        response = requests.get(verify_url, timeout=10)
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Verification result:")
            print(json.dumps(result, indent=2))
        else:
            print(f"❌ Verification failed: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Verification error: {e}")

if __name__ == "__main__":
    add_indicators()
