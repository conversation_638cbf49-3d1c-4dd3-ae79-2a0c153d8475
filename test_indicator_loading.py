#!/usr/bin/env python3
"""
Test script to verify indicator loading functionality
"""
import sys
import os
import json
from datetime import datetime

# Add the backend directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from app.core.database import get_db_cursor
from app.services.indicator_config_manager import IndicatorConfigManager

def test_indicator_loading():
    """Test the indicator loading functionality"""
    print("🧪 Testing Indicator Loading Functionality")
    print("=" * 50)
    
    try:
        # First, let's check if we have any strategies
        with get_db_cursor() as cursor:
            cursor.execute("SELECT id, name, symbol, timeframe FROM strategies LIMIT 5")
            strategies = cursor.fetchall()
            
            print(f"📊 Found {len(strategies)} strategies:")
            for strategy in strategies:
                print(f"  - ID: {strategy[0]}, Name: {strategy[1]}, Symbol: {strategy[2]}, Timeframe: {strategy[3]}")
            
            if not strategies:
                print("❌ No strategies found. Creating a test strategy...")
                cursor.execute("""
                    INSERT INTO strategies (name, description, symbol, timeframe, exchange)
                    VALUES (%s, %s, %s, %s, %s)
                """, ("Test Strategy", "Test strategy for indicator loading", "BTCUSDT", "1h", "binance"))
                
                strategy_id = cursor.lastrowid
                print(f"✅ Created test strategy with ID: {strategy_id}")
            else:
                strategy_id = strategies[0][0]
                print(f"🎯 Using strategy ID: {strategy_id}")
            
            # Check existing indicator configurations for this strategy
            print(f"\n📈 Checking existing indicators for strategy {strategy_id}:")
            configs = IndicatorConfigManager.get_strategy_indicator_configs(strategy_id)
            
            if configs:
                print(f"✅ Found {len(configs)} existing indicator configurations:")
                for name, config in configs.items():
                    print(f"  - {name}: enabled={config['is_enabled']}, config={config['config']}")
            else:
                print("❌ No existing indicator configurations found.")
                print("💡 Creating sample indicator configurations...")
                
                # Create sample indicator configurations
                sample_indicators = [
                    {
                        'name': 'EMA',
                        'config': {'periods': [20, 50]},
                        'enabled': True,
                        'order': 1
                    },
                    {
                        'name': 'RSI',
                        'config': {'period': 14, 'overbought': 70, 'oversold': 30},
                        'enabled': True,
                        'order': 2
                    },
                    {
                        'name': 'MACD',
                        'config': {'fast_period': 12, 'slow_period': 26, 'signal_period': 9},
                        'enabled': True,
                        'order': 3
                    }
                ]
                
                for indicator in sample_indicators:
                    success = IndicatorConfigManager.save_strategy_indicator_config(
                        strategy_id=strategy_id,
                        indicator_name=indicator['name'],
                        config=indicator['config'],
                        is_enabled=indicator['enabled'],
                        display_order=indicator['order']
                    )
                    
                    if success:
                        print(f"  ✅ Created {indicator['name']} configuration")
                    else:
                        print(f"  ❌ Failed to create {indicator['name']} configuration")
                
                # Verify the configurations were created
                print(f"\n🔍 Verifying created configurations:")
                configs = IndicatorConfigManager.get_strategy_indicator_configs(strategy_id)
                print(f"✅ Now found {len(configs)} indicator configurations:")
                for name, config in configs.items():
                    print(f"  - {name}: enabled={config['is_enabled']}, config={config['config']}")
            
            print(f"\n🌐 API Response Format Test:")
            print("=" * 30)
            api_response = {
                "success": True,
                "data": configs,
                "strategy_id": strategy_id,
                "count": len(configs)
            }
            print(json.dumps(api_response, indent=2, default=str))
            
            print(f"\n✅ Test completed successfully!")
            print(f"📝 Summary:")
            print(f"  - Strategy ID: {strategy_id}")
            print(f"  - Indicator count: {len(configs)}")
            print(f"  - API endpoint: /api/v1/indicators/strategies/{strategy_id}/indicators")
            
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_indicator_loading()
