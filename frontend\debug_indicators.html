<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Indicators - Complete Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .btn {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover { background: #45a049; }
        .btn.secondary { background: #2196F3; }
        .btn.secondary:hover { background: #1976D2; }
        .btn.danger { background: #f44336; }
        .btn.danger:hover { background: #da190b; }
        .log {
            background: #1e1e1e;
            color: #00ff00;
            padding: 15px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 500px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .status.success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .status.error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .status.info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .grid { display: grid; grid-template-columns: 1fr 1fr; gap: 10px; }
    </style>
</head>
<body>
    <h1>🔍 Complete Indicator Debug Tool</h1>
    
    <div class="container">
        <h2>Step 1: Database & API Tests</h2>
        <div class="grid">
            <button class="btn" onclick="testDatabaseConnection()">Test Database Connection</button>
            <button class="btn secondary" onclick="checkExistingIndicators()">Check Existing Indicators</button>
            <button class="btn" onclick="createSampleIndicators()">Create Sample Indicators</button>
            <button class="btn secondary" onclick="verifyCreatedIndicators()">Verify Created Indicators</button>
        </div>
    </div>

    <div class="container">
        <h2>Step 2: Frontend Event Tests</h2>
        <div class="grid">
            <button class="btn secondary" onclick="testStrategyChangeEvent()">Test Strategy Change Event</button>
            <button class="btn secondary" onclick="testDirectIndicatorLoad()">Test Direct Indicator Load</button>
            <button class="btn secondary" onclick="checkEventListeners()">Check Event Listeners</button>
            <button class="btn secondary" onclick="testUIRendering()">Test UI Rendering</button>
        </div>
    </div>

    <div class="container">
        <h2>Step 3: Plotting System Tests</h2>
        <div class="grid">
            <button class="btn secondary" onclick="testIndicatorPlotting()">Test Indicator Plotting</button>
            <button class="btn secondary" onclick="checkChartData()">Check Chart Data</button>
            <button class="btn secondary" onclick="testAdvancedPlotter()">Test Advanced Plotter</button>
            <button class="btn secondary" onclick="simulateFullFlow()">Simulate Full Flow</button>
        </div>
    </div>

    <div class="container">
        <h2>Utilities</h2>
        <div class="grid">
            <button class="btn danger" onclick="clearAllIndicators()">Clear All Indicators</button>
            <button class="btn secondary" onclick="clearLog()">Clear Log</button>
            <button class="btn secondary" onclick="exportLog()">Export Log</button>
            <button class="btn secondary" onclick="runFullDiagnostic()">Run Full Diagnostic</button>
        </div>
    </div>

    <div class="container">
        <h2>Status</h2>
        <div id="status" class="status info">Ready to debug...</div>
    </div>

    <div class="container">
        <h2>Debug Log</h2>
        <div id="log" class="log">Debug log will appear here...\n</div>
    </div>

    <script>
        const logEl = document.getElementById('log');
        const statusEl = document.getElementById('status');

        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            logEl.textContent += `[${timestamp}] ${message}\n`;
            logEl.scrollTop = logEl.scrollHeight;
            console.log(message);
        }

        function setStatus(message, type = 'info') {
            statusEl.textContent = message;
            statusEl.className = `status ${type}`;
        }

        function clearLog() {
            logEl.textContent = 'Log cleared...\n';
        }

        // Step 1: Database & API Tests
        async function testDatabaseConnection() {
            log('🔍 Testing database connection via API...');
            setStatus('Testing database connection...', 'info');
            
            try {
                const response = await fetch('/api/v1/strategies/list');
                if (response.ok) {
                    const strategies = await response.json();
                    log(`✅ Database connection OK - Found ${strategies.length} strategies`);
                    strategies.forEach(s => log(`   - Strategy ${s.id}: ${s.name} (${s.symbol}/${s.timeframe})`));
                    setStatus('✅ Database connection successful', 'success');
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            } catch (error) {
                log(`❌ Database connection failed: ${error.message}`);
                setStatus('❌ Database connection failed', 'error');
            }
        }

        async function checkExistingIndicators() {
            log('🔍 Checking existing indicators for strategy 1...');
            
            try {
                const response = await fetch('/api/v1/indicators/strategies/1/indicators');
                const result = await response.json();
                
                log(`📊 API Response: ${JSON.stringify(result, null, 2)}`);
                
                if (result.success) {
                    const count = Object.keys(result.data).length;
                    log(`✅ Found ${count} existing indicators`);
                    
                    if (count > 0) {
                        for (const [name, config] of Object.entries(result.data)) {
                            log(`   - ${name}: enabled=${config.is_enabled}, config=${JSON.stringify(config.config)}`);
                        }
                        setStatus(`✅ Found ${count} existing indicators`, 'success');
                    } else {
                        log('ℹ️ No existing indicators found - need to create some');
                        setStatus('ℹ️ No existing indicators found', 'info');
                    }
                } else {
                    log(`❌ API error: ${JSON.stringify(result)}`);
                    setStatus('❌ API error checking indicators', 'error');
                }
            } catch (error) {
                log(`❌ Error checking indicators: ${error.message}`);
                setStatus('❌ Error checking indicators', 'error');
            }
        }

        async function createSampleIndicators() {
            log('🧪 Creating sample indicators...');
            setStatus('Creating sample indicators...', 'info');
            
            const indicators = [
                { indicator_name: "EMA", config: { periods: [20, 50] }, is_enabled: true, display_order: 1 },
                { indicator_name: "RSI", config: { period: 14, overbought: 70, oversold: 30 }, is_enabled: true, display_order: 2 },
                { indicator_name: "MACD", config: { fast_period: 12, slow_period: 26, signal_period: 9 }, is_enabled: true, display_order: 3 },
                { indicator_name: "BOLLINGER_BANDS", config: { period: 20, std_dev: 2 }, is_enabled: true, display_order: 4 }
            ];

            let successCount = 0;

            for (const indicator of indicators) {
                try {
                    log(`📊 Creating ${indicator.indicator_name}...`);
                    
                    const response = await fetch('/api/v1/indicators/strategies/1/indicators', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify(indicator)
                    });

                    if (response.ok) {
                        const result = await response.json();
                        if (result.success) {
                            log(`   ✅ Created ${indicator.indicator_name}`);
                            successCount++;
                        } else {
                            log(`   ❌ API error: ${JSON.stringify(result)}`);
                        }
                    } else {
                        const errorText = await response.text();
                        log(`   ❌ HTTP ${response.status}: ${errorText}`);
                    }
                } catch (error) {
                    log(`   ❌ Error: ${error.message}`);
                }
            }

            if (successCount === indicators.length) {
                setStatus(`✅ Created ${successCount} indicators successfully!`, 'success');
                log(`\n🎯 All indicators created! Now test the frontend loading...`);
            } else {
                setStatus(`⚠️ Created ${successCount}/${indicators.length} indicators`, 'error');
            }
        }

        async function verifyCreatedIndicators() {
            log('🔍 Verifying created indicators...');
            await checkExistingIndicators();
        }

        // Step 2: Frontend Event Tests
        function testStrategyChangeEvent() {
            log('🔄 Testing strategy change event...');
            
            // Dispatch the event
            document.dispatchEvent(new CustomEvent('strategyChanged', {
                detail: { strategyId: 1 }
            }));
            
            log('✅ Strategy change event dispatched');
            log('💡 Check console for MultiIndicatorConfigManager responses');
            setStatus('Strategy change event dispatched', 'success');
        }

        function testDirectIndicatorLoad() {
            log('🎯 Testing direct indicator load...');
            
            if (window.multiIndicatorConfig) {
                log('✅ MultiIndicatorConfigManager found');
                window.multiIndicatorConfig.loadStrategyIndicators(1);
                log('✅ Direct load method called');
                setStatus('Direct load method called', 'success');
            } else {
                log('❌ MultiIndicatorConfigManager not found');
                setStatus('❌ MultiIndicatorConfigManager not found', 'error');
            }
        }

        function checkEventListeners() {
            log('🔍 Checking event listeners and managers...');
            
            log(`MultiIndicatorConfigManager: ${!!window.multiIndicatorConfig}`);
            log(`StrategyManager: ${!!window.strategyManager}`);
            log(`AdvancedIndicatorPlotter: ${!!window.advancedIndicatorPlotter}`);
            
            if (window.multiIndicatorConfig) {
                log(`Current strategy: ${window.multiIndicatorConfig.currentStrategy}`);
                log(`Current configs: ${JSON.stringify(window.multiIndicatorConfig.currentConfigs)}`);
            }
            
            if (window.strategyManager) {
                log(`Strategy manager current strategy: ${window.strategyManager.currentStrategy?.id}`);
            }
            
            setStatus('Event listener check complete', 'info');
        }

        function testUIRendering() {
            log('🎨 Testing UI rendering...');
            
            const container = document.getElementById('indicator-config-container');
            log(`Indicator config container found: ${!!container}`);
            
            if (container) {
                log(`Container content: ${container.innerHTML.substring(0, 200)}...`);
            }
            
            const statusMessage = document.getElementById('strategy-status-message');
            log(`Status message element found: ${!!statusMessage}`);
            
            if (statusMessage) {
                log(`Status message content: ${statusMessage.innerHTML}`);
            }
            
            setStatus('UI rendering check complete', 'info');
        }

        // Step 3: Plotting System Tests
        function testIndicatorPlotting() {
            log('📈 Testing indicator plotting system...');
            
            if (window.advancedIndicatorPlotter) {
                log('✅ AdvancedIndicatorPlotter found');
                log(`Current configs: ${JSON.stringify(window.advancedIndicatorPlotter.currentConfigs)}`);
                log(`Current indicators: ${JSON.stringify(Object.keys(window.advancedIndicatorPlotter.currentIndicators || {}))}`);
            } else {
                log('❌ AdvancedIndicatorPlotter not found');
            }
            
            setStatus('Plotting system check complete', 'info');
        }

        function checkChartData() {
            log('📊 Checking chart data availability...');
            
            if (window.professionalChart) {
                log('✅ Professional chart found');
                // Add more chart data checks here
            } else {
                log('❌ Professional chart not found');
            }
            
            setStatus('Chart data check complete', 'info');
        }

        function testAdvancedPlotter() {
            log('🔧 Testing AdvancedIndicatorPlotter directly...');
            
            if (window.advancedIndicatorPlotter) {
                // Test the plotter with sample data
                const testConfigs = {
                    'EMA': {
                        indicator_name: 'EMA',
                        config: { periods: [20, 50] },
                        is_enabled: true
                    }
                };
                
                window.advancedIndicatorPlotter.currentConfigs = testConfigs;
                log('✅ Test configs set on AdvancedIndicatorPlotter');
                log(`Configs: ${JSON.stringify(testConfigs)}`);
            } else {
                log('❌ AdvancedIndicatorPlotter not available');
            }
            
            setStatus('Advanced plotter test complete', 'info');
        }

        function simulateFullFlow() {
            log('🔄 Simulating full indicator loading flow...');
            
            // Step 1: Trigger strategy change
            testStrategyChangeEvent();
            
            // Step 2: Wait and check results
            setTimeout(() => {
                testDirectIndicatorLoad();
                
                setTimeout(() => {
                    checkEventListeners();
                    testUIRendering();
                }, 1000);
            }, 500);
            
            setStatus('Full flow simulation started', 'info');
        }

        // Utilities
        async function clearAllIndicators() {
            if (!confirm('Clear all indicators from database?')) return;
            
            log('🗑️ Clearing all indicators...');
            // Implementation would go here
            setStatus('Clear function not implemented yet', 'info');
        }

        function exportLog() {
            const logContent = logEl.textContent;
            const blob = new Blob([logContent], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `indicator-debug-${new Date().toISOString().slice(0,19)}.txt`;
            a.click();
            URL.revokeObjectURL(url);
        }

        async function runFullDiagnostic() {
            log('🔍 Running full diagnostic...');
            setStatus('Running full diagnostic...', 'info');
            
            await testDatabaseConnection();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await checkExistingIndicators();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            checkEventListeners();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            testUIRendering();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            testIndicatorPlotting();
            
            log('✅ Full diagnostic complete');
            setStatus('✅ Full diagnostic complete', 'success');
        }

        // Initialize
        log('🚀 Debug tool initialized');
        log('💡 Start with "Run Full Diagnostic" or test individual components');
    </script>
</body>
</html>
