<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Create Test Indicators</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .btn {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #45a049;
        }
        .btn.secondary {
            background: #2196F3;
        }
        .btn.secondary:hover {
            background: #1976D2;
        }
        .log {
            background: #1e1e1e;
            color: #00ff00;
            padding: 15px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <h1>🧪 Create Test Indicators</h1>
    
    <div class="container">
        <h2>Actions</h2>
        <button class="btn" onclick="createSampleIndicators()">Create Sample Indicators</button>
        <button class="btn secondary" onclick="verifyIndicators()">Verify Indicators</button>
        <button class="btn secondary" onclick="clearLog()">Clear Log</button>
    </div>

    <div class="container">
        <h2>Status</h2>
        <div id="status" class="status">Ready to create test indicators...</div>
    </div>

    <div class="container">
        <h2>Log</h2>
        <div id="log" class="log">Ready to create test indicators...\n</div>
    </div>

    <script>
        const logEl = document.getElementById('log');
        const statusEl = document.getElementById('status');

        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            logEl.textContent += `[${timestamp}] ${message}\n`;
            logEl.scrollTop = logEl.scrollHeight;
            console.log(message);
        }

        function setStatus(message, type = 'success') {
            statusEl.textContent = message;
            statusEl.className = `status ${type}`;
        }

        function clearLog() {
            logEl.textContent = 'Log cleared...\n';
        }

        async function createSampleIndicators() {
            log('🧪 Creating sample indicators for strategy 1...');
            setStatus('Creating sample indicators...', 'info');

            const strategyId = 1;
            const indicators = [
                {
                    indicator_name: "EMA",
                    config: { periods: [20, 50] },
                    is_enabled: true,
                    display_order: 1
                },
                {
                    indicator_name: "RSI",
                    config: { period: 14, overbought: 70, oversold: 30 },
                    is_enabled: true,
                    display_order: 2
                },
                {
                    indicator_name: "MACD",
                    config: { fast_period: 12, slow_period: 26, signal_period: 9 },
                    is_enabled: true,
                    display_order: 3
                },
                {
                    indicator_name: "BOLLINGER_BANDS",
                    config: { period: 20, std_dev: 2 },
                    is_enabled: true,
                    display_order: 4
                }
            ];

            let successCount = 0;

            for (const indicator of indicators) {
                try {
                    log(`📊 Creating ${indicator.indicator_name}...`);
                    
                    const response = await fetch(`/api/v1/indicators/strategies/${strategyId}/indicators`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify(indicator)
                    });

                    if (response.ok) {
                        const result = await response.json();
                        if (result.success) {
                            log(`   ✅ Successfully created ${indicator.indicator_name}`);
                            successCount++;
                        } else {
                            log(`   ❌ API error: ${JSON.stringify(result)}`);
                        }
                    } else {
                        const errorText = await response.text();
                        log(`   ❌ HTTP ${response.status}: ${errorText}`);
                    }

                } catch (error) {
                    log(`   ❌ Error creating ${indicator.indicator_name}: ${error.message}`);
                }
            }

            if (successCount === indicators.length) {
                setStatus(`✅ Successfully created ${successCount} indicators!`, 'success');
                log(`\n🎯 All indicators created! Now test the frontend:`);
                log(`   1. Go to the main application`);
                log(`   2. Select Strategy 1`);
                log(`   3. Open Indicators tab`);
                log(`   4. You should see ${successCount} loaded indicators`);
            } else {
                setStatus(`⚠️ Created ${successCount}/${indicators.length} indicators`, 'error');
            }
        }

        async function verifyIndicators() {
            log('🔍 Verifying indicators for strategy 1...');
            setStatus('Verifying indicators...', 'info');

            try {
                const response = await fetch('/api/v1/indicators/strategies/1/indicators');
                
                if (response.ok) {
                    const result = await response.json();
                    
                    if (result.success) {
                        const count = Object.keys(result.data).length;
                        log(`✅ Found ${count} indicators:`);
                        
                        for (const [name, config] of Object.entries(result.data)) {
                            log(`   - ${name}: enabled=${config.is_enabled}, config=${JSON.stringify(config.config)}`);
                        }
                        
                        setStatus(`✅ Verification complete: ${count} indicators found`, 'success');
                    } else {
                        log(`❌ API error: ${JSON.stringify(result)}`);
                        setStatus('❌ Verification failed', 'error');
                    }
                } else {
                    const errorText = await response.text();
                    log(`❌ HTTP ${response.status}: ${errorText}`);
                    setStatus('❌ Verification failed', 'error');
                }

            } catch (error) {
                log(`❌ Verification error: ${error.message}`);
                setStatus('❌ Verification failed', 'error');
            }
        }

        // Initialize
        log('🚀 Test page ready');
        log('💡 Click "Create Sample Indicators" to add test data');
    </script>
</body>
</html>
