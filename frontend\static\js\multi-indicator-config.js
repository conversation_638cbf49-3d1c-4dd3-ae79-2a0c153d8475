/**
 * Multi-Indicator Configuration Manager
 * Handles visual configuration of multiple technical indicators
 */

class MultiIndicatorConfigManager {
    constructor() {
        this.currentStrategy = null;
        this.indicatorDefaults = {};
        this.currentConfigs = {};
        this.supportedIndicators = [];
        this.availableIndicators = []; // Indicators that can be added

        this.init();
    }

    async init() {
        console.log('MultiIndicatorConfigManager: Initializing...');
        await this.loadSupportedIndicators();
        await this.loadIndicatorDefaults();
        this.setupEventListeners();
        this.updateStrategyStatus();
        this.renderIndicatorConfigs();
        this.populateAvailableIndicators();

        // Try to sync with current strategy if one exists
        this.syncWithCurrentStrategy();
        console.log('MultiIndicatorConfigManager: Initialization complete');
    }

    syncWithCurrentStrategy() {
        // Check if there's a current strategy in the strategy manager
        if (window.strategyManager && window.strategyManager.currentStrategy) {
            console.log('MultiIndicatorConfigManager: Found existing strategy, syncing:', window.strategyManager.currentStrategy.id);
            this.loadStrategyIndicators(window.strategyManager.currentStrategy.id);
        } else {
            // Check if there's a selected strategy in the dropdown
            const strategySelect = document.getElementById('strategy-select');
            if (strategySelect && strategySelect.value) {
                console.log('MultiIndicatorConfigManager: Found selected strategy in dropdown, syncing:', strategySelect.value);
                this.loadStrategyIndicators(parseInt(strategySelect.value));
            } else {
                console.log('MultiIndicatorConfigManager: No existing strategy found');
                // Try again after a short delay in case strategy manager is still initializing
                setTimeout(() => {
                    if (window.strategyManager && window.strategyManager.currentStrategy) {
                        console.log('MultiIndicatorConfigManager: Found strategy after delay, syncing:', window.strategyManager.currentStrategy.id);
                        this.loadStrategyIndicators(window.strategyManager.currentStrategy.id);
                    }
                }, 500);
            }
        }
    }

    async loadSupportedIndicators() {
        try {
            const response = await fetch('/api/v1/indicators/supported');
            const data = await response.json();
            
            if (data.success) {
                this.supportedIndicators = data.data;
                console.log('Loaded supported indicators:', this.supportedIndicators.length);
            }
        } catch (error) {
            console.error('Error loading supported indicators:', error);
        }
    }

    async loadIndicatorDefaults() {
        try {
            const response = await fetch('/api/v1/indicators/defaults');
            const data = await response.json();
            
            if (data.success) {
                this.indicatorDefaults = data.data;
                console.log('Loaded indicator defaults:', Object.keys(this.indicatorDefaults).length);
            }
        } catch (error) {
            console.error('Error loading indicator defaults:', error);
        }
    }

    async loadStrategyIndicators(strategyId) {
        console.log('MultiIndicatorConfigManager: Loading strategy indicators for strategy ID:', strategyId);

        if (!strategyId) {
            console.log('MultiIndicatorConfigManager: No strategy ID provided, clearing current strategy');
            this.currentStrategy = null;
            this.currentConfigs = {};
            this.updateStrategyStatus();
            this.renderIndicatorConfigs();
            return;
        }

        try {
            console.log('MultiIndicatorConfigManager: Fetching indicators for strategy', strategyId);
            const response = await fetch(`/api/v1/indicators/strategies/${strategyId}/indicators`);

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const data = await response.json();
            console.log('MultiIndicatorConfigManager: API response:', data);

            if (data.success) {
                this.currentConfigs = data.data;
                this.currentStrategy = strategyId;
                console.log('MultiIndicatorConfigManager: Set current strategy to:', this.currentStrategy);
                console.log('MultiIndicatorConfigManager: Loaded indicator configs:', this.currentConfigs);

                this.updateStrategyStatus();
                this.renderIndicatorConfigs();
                this.populateAvailableIndicators();

                const indicatorCount = Object.keys(this.currentConfigs).length;
                console.log(`✅ Successfully loaded ${indicatorCount} indicators for strategy ${strategyId}`);

                if (indicatorCount > 0) {
                    this.showMessage(`Loaded ${indicatorCount} saved indicators for strategy`, 'success');
                } else {
                    this.showMessage('No saved indicators found for this strategy', 'info');
                }
            } else {
                console.error('MultiIndicatorConfigManager: API returned error:', data);
                this.showMessage('Failed to load strategy indicators', 'error');
            }
        } catch (error) {
            console.error('Error loading strategy indicators:', error);
            this.showMessage(`Error loading strategy indicators: ${error.message}`, 'error');
        }
    }

    setupEventListeners() {
        // Listen for strategy changes
        document.addEventListener('strategyChanged', (event) => {
            console.log('🔄 MultiIndicatorConfigManager: Received strategyChanged event', event.detail);
            console.log('📊 Loading indicators for strategy ID:', event.detail.strategyId);
            this.loadStrategyIndicators(event.detail.strategyId);
        });

        // Listen for chart data updates
        document.addEventListener('chartDataLoaded', () => {
            this.updatePlotButtonState();
        });

        // Listen for strategy manager initialization
        document.addEventListener('strategyManagerReady', () => {
            console.log('MultiIndicatorConfigManager: Strategy manager ready, attempting sync');
            this.syncWithCurrentStrategy();
        });

        // Listen for strategy dropdown updates
        document.addEventListener('strategyDropdownUpdated', () => {
            console.log('MultiIndicatorConfigManager: Strategy dropdown updated, checking for selection');
            if (!this.currentStrategy) {
                this.syncWithCurrentStrategy();
            }
        });

        // Monitor strategy dropdown changes directly
        const strategySelect = document.getElementById('strategy-select');
        if (strategySelect) {
            // Check for existing selection on page load
            if (strategySelect.value && !this.currentStrategy) {
                console.log('MultiIndicatorConfigManager: Found existing dropdown selection:', strategySelect.value);
                this.loadStrategyIndicators(parseInt(strategySelect.value));
            }
        }

        // Save configuration button
        const saveBtn = document.getElementById('save-indicator-config');
        if (saveBtn) {
            saveBtn.addEventListener('click', () => this.saveAllConfigurations());
        }

        // Reset to defaults button
        const resetBtn = document.getElementById('reset-indicator-config');
        if (resetBtn) {
            resetBtn.addEventListener('click', () => this.resetToDefaults());
        }

        // Add indicator button
        const addBtn = document.getElementById('add-indicator-btn');
        if (addBtn) {
            addBtn.addEventListener('click', () => this.addIndicator());
        }

        // Plot indicators button
        const plotBtn = document.getElementById('plot-indicators-btn');
        if (plotBtn) {
            plotBtn.addEventListener('click', () => this.plotIndicators());
        }

        // Available indicators dropdown
        const indicatorSelect = document.getElementById('available-indicators');
        if (indicatorSelect) {
            indicatorSelect.addEventListener('change', () => {
                const addBtn = document.getElementById('add-indicator-btn');
                if (addBtn) {
                    addBtn.disabled = !indicatorSelect.value;
                }
            });
        }
    }

    updateStrategyStatus() {
        console.log('MultiIndicatorConfigManager: Updating strategy status, current strategy:', this.currentStrategy);

        const statusMessage = document.getElementById('strategy-status-message');
        const addSection = document.getElementById('add-indicator-section');

        console.log('MultiIndicatorConfigManager: Status elements found:', {
            statusMessage: !!statusMessage,
            addSection: !!addSection
        });

        if (!statusMessage || !addSection) {
            console.error('MultiIndicatorConfigManager: Required DOM elements not found');
            return;
        }

        if (this.currentStrategy) {
            console.log('MultiIndicatorConfigManager: Showing strategy selected status');
            statusMessage.innerHTML = `
                <span class="status-icon">✅</span>
                <span class="status-text">Strategy ${this.currentStrategy} selected</span>
            `;
            statusMessage.className = 'status-message success';
            addSection.style.display = 'block';
        } else {
            console.log('MultiIndicatorConfigManager: Showing no strategy selected status');
            statusMessage.innerHTML = `
                <span class="status-icon">⚠️</span>
                <span class="status-text">Please select a strategy first</span>
            `;
            statusMessage.className = 'status-message warning';
            addSection.style.display = 'none';
        }

        // Update plot button state
        this.updatePlotButtonState();
    }

    updatePlotButtonState() {
        const plotBtn = document.getElementById('plot-indicators-btn');
        if (!plotBtn) return;

        const hasStrategy = !!this.currentStrategy;
        const hasEnabledIndicators = Object.keys(this.currentConfigs).some(
            indicatorName => this.currentConfigs[indicatorName].is_enabled
        );
        const hasChartData = !!(window.professionalChart && window.professionalChart.candlestickSeries);

        plotBtn.disabled = !hasStrategy || !hasEnabledIndicators || !hasChartData;

        if (!hasStrategy) {
            plotBtn.title = 'Please select a strategy first';
        } else if (!hasEnabledIndicators) {
            plotBtn.title = 'Please enable at least one indicator';
        } else if (!hasChartData) {
            plotBtn.title = 'Please load chart data first';
        } else {
            plotBtn.title = 'Plot enabled indicators on chart';
        }
    }

    populateAvailableIndicators() {
        const select = document.getElementById('available-indicators');
        if (!select) return;

        // Clear existing options except the first one
        select.innerHTML = '<option value="">Select an indicator to add...</option>';

        // Get indicators that are not already added to the strategy
        const addedIndicators = Object.keys(this.currentConfigs);
        this.availableIndicators = this.supportedIndicators.filter(
            indicator => !addedIndicators.includes(indicator.name)
        );

        // Populate dropdown with available indicators
        this.availableIndicators.forEach(indicator => {
            const option = document.createElement('option');
            option.value = indicator.name;
            option.textContent = `${indicator.display_name} (${indicator.chart_type})`;
            select.appendChild(option);
        });

        // Update add button state
        const addBtn = document.getElementById('add-indicator-btn');
        if (addBtn) {
            addBtn.disabled = this.availableIndicators.length === 0;
            if (this.availableIndicators.length === 0) {
                select.innerHTML = '<option value="">All indicators already added</option>';
            }
        }
    }

    async addIndicator() {
        if (!this.currentStrategy) {
            this.showMessage('Please select a strategy first', 'error');
            return;
        }

        const select = document.getElementById('available-indicators');
        const indicatorName = select.value;

        if (!indicatorName) {
            this.showMessage('Please select an indicator to add', 'error');
            return;
        }

        try {
            // Get indicator defaults
            const defaults = this.indicatorDefaults[indicatorName] || {};
            const defaultConfig = defaults.default_config || {};

            // Save indicator to strategy
            const response = await fetch(`/api/v1/indicators/strategies/${this.currentStrategy}/indicators`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    indicator_name: indicatorName,
                    config: defaultConfig,
                    is_enabled: true,
                    display_order: Object.keys(this.currentConfigs).length
                })
            });

            const data = await response.json();

            if (data.success) {
                // Add to current configs
                this.currentConfigs[indicatorName] = {
                    indicator_name: indicatorName,
                    config: defaultConfig,
                    is_enabled: true,
                    display_order: Object.keys(this.currentConfigs).length,
                    display_name: defaults.display_name || indicatorName,
                    chart_type: defaults.chart_type || 'overlay',
                    supports_multiple: defaults.supports_multiple || false
                };

                // Refresh UI
                this.renderIndicatorConfigs();
                this.populateAvailableIndicators();

                // Reset dropdown
                select.value = '';
                document.getElementById('add-indicator-btn').disabled = true;

                this.showMessage(`${defaults.display_name || indicatorName} added successfully`, 'success');

                // Automatically plot the new indicator if chart data is available
                if (window.professionalChart && window.professionalChart.candlestickSeries) {
                    setTimeout(() => {
                        this.updateChart();
                    }, 500); // Small delay to ensure UI updates first
                }
            } else {
                this.showMessage('Failed to add indicator: ' + (data.message || 'Unknown error'), 'error');
            }
        } catch (error) {
            console.error('Error adding indicator:', error);
            this.showMessage('Error adding indicator', 'error');
        }
    }

    renderIndicatorConfigs() {
        const container = document.getElementById('indicator-config-container');
        if (!container) return;

        container.innerHTML = '';

        if (!this.currentStrategy) {
            container.innerHTML = '<div class="no-strategy-message">Select a strategy to configure indicators</div>';
            return;
        }

        // Show only indicators that are added to the current strategy
        const strategyIndicators = Object.keys(this.currentConfigs);

        if (strategyIndicators.length === 0) {
            container.innerHTML = '<div class="no-indicators-message">No indicators added yet. Use the "Add New Indicator" section above to add indicators.</div>';
            return;
        }

        // Create accordion-style configuration panels for strategy indicators
        strategyIndicators.forEach(indicatorName => {
            const indicator = this.supportedIndicators.find(ind => ind.name === indicatorName);
            if (indicator) {
                const panel = this.createIndicatorPanel(indicator);
                container.appendChild(panel);
            }
        });

        // Update plot button state after rendering
        this.updatePlotButtonState();
    }

    createIndicatorPanel(indicator) {
        const panel = document.createElement('div');
        panel.className = 'indicator-panel';
        panel.dataset.indicator = indicator.name;

        const config = this.currentConfigs[indicator.name] || {};
        const defaults = this.indicatorDefaults[indicator.name] || {};
        const mergedConfig = { ...defaults.default_config, ...config.config };

        panel.innerHTML = `
            <div class="indicator-header" onclick="this.parentElement.classList.toggle('expanded')">
                <div class="indicator-title">
                    <input type="checkbox"
                           id="enable-${indicator.name}"
                           ${config.is_enabled !== false ? 'checked' : ''}
                           onchange="multiIndicatorConfig.toggleIndicator('${indicator.name}', this.checked)">
                    <label for="enable-${indicator.name}">
                        <span class="indicator-name">${indicator.display_name}</span>
                        <span class="indicator-type ${indicator.chart_type}">${indicator.chart_type}</span>
                    </label>
                </div>
                <div class="indicator-controls">
                    <button class="btn-reset" onclick="multiIndicatorConfig.resetIndicator('${indicator.name}'); event.stopPropagation();">
                        Reset
                    </button>
                    <span class="expand-icon">▼</span>
                </div>
            </div>
            <button class="remove-indicator" onclick="multiIndicatorConfig.removeIndicator('${indicator.name}'); event.stopPropagation();" title="Remove indicator">
                ×
            </button>
            <div class="indicator-content">
                ${this.renderIndicatorSpecificConfig(indicator, mergedConfig)}
            </div>
        `;

        return panel;
    }

    renderIndicatorSpecificConfig(indicator, config) {
        switch (indicator.name) {
            case 'EMA':
            case 'SMA':
                return this.renderMultiPeriodConfig(indicator, config);
            case 'RSI':
                return this.renderRSIConfig(indicator, config);
            case 'MACD':
                return this.renderMACDConfig(indicator, config);
            case 'BOLLINGER_BANDS':
                return this.renderBollingerBandsConfig(indicator, config);
            case 'STOCHASTIC':
                return this.renderStochasticConfig(indicator, config);
            default:
                return this.renderGenericConfig(indicator, config);
        }
    }

    renderMultiPeriodConfig(indicator, config) {
        const periods = config.periods || [20, 50, 100];
        const colors = config.colors || ['#FF6B6B', '#4ECDC4', '#45B7D1'];
        const lineWidth = config.lineWidth || 2;

        let html = `
            <div class="config-section">
                <h4>Periods & Colors</h4>
                <div class="multi-period-config" id="${indicator.name}-periods">
        `;

        periods.forEach((period, index) => {
            const color = colors[index] || '#2196F3';
            html += `
                <div class="period-row">
                    <input type="number" 
                           value="${period}" 
                           min="1" max="500" 
                           class="period-input"
                           data-index="${index}"
                           onchange="multiIndicatorConfig.updatePeriod('${indicator.name}', ${index}, this.value)">
                    <input type="color" 
                           value="${color}" 
                           class="color-input"
                           data-index="${index}"
                           onchange="multiIndicatorConfig.updateColor('${indicator.name}', ${index}, this.value)">
                    <button class="btn-remove" onclick="multiIndicatorConfig.removePeriod('${indicator.name}', ${index})">×</button>
                </div>
            `;
        });

        html += `
                    <button class="btn-add-period" onclick="multiIndicatorConfig.addPeriod('${indicator.name}')">
                        + Add Period
                    </button>
                </div>
                <div class="config-row">
                    <label>Line Width:</label>
                    <input type="range" min="1" max="5" value="${lineWidth}" 
                           onchange="multiIndicatorConfig.updateLineWidth('${indicator.name}', this.value)">
                    <span>${lineWidth}px</span>
                </div>
            </div>
        `;

        return html;
    }

    renderRSIConfig(indicator, config) {
        const periods = config.periods || [14];
        const colors = config.colors || ['#2196F3'];
        const overbought = config.overbought || 70;
        const oversold = config.oversold || 30;
        const lineWidth = config.lineWidth || 2;

        let html = `
            <div class="config-section">
                <h4>RSI Settings</h4>
                <div class="multi-period-config" id="${indicator.name}-periods">
        `;

        periods.forEach((period, index) => {
            const color = colors[index] || '#2196F3';
            html += `
                <div class="period-row">
                    <input type="number" 
                           value="${period}" 
                           min="2" max="100" 
                           class="period-input"
                           data-index="${index}"
                           onchange="multiIndicatorConfig.updatePeriod('${indicator.name}', ${index}, this.value)">
                    <input type="color" 
                           value="${color}" 
                           class="color-input"
                           data-index="${index}"
                           onchange="multiIndicatorConfig.updateColor('${indicator.name}', ${index}, this.value)">
                    <button class="btn-remove" onclick="multiIndicatorConfig.removePeriod('${indicator.name}', ${index})">×</button>
                </div>
            `;
        });

        html += `
                    <button class="btn-add-period" onclick="multiIndicatorConfig.addPeriod('${indicator.name}')">
                        + Add RSI Period
                    </button>
                </div>
                <div class="config-row">
                    <label>Overbought Level:</label>
                    <input type="number" min="50" max="100" value="${overbought}" 
                           onchange="multiIndicatorConfig.updateOverbought('${indicator.name}', this.value)">
                </div>
                <div class="config-row">
                    <label>Oversold Level:</label>
                    <input type="number" min="0" max="50" value="${oversold}" 
                           onchange="multiIndicatorConfig.updateOversold('${indicator.name}', this.value)">
                </div>
                <div class="config-row">
                    <label>Line Width:</label>
                    <input type="range" min="1" max="5" value="${lineWidth}" 
                           onchange="multiIndicatorConfig.updateLineWidth('${indicator.name}', this.value)">
                    <span>${lineWidth}px</span>
                </div>
            </div>
        `;

        return html;
    }

    renderMACDConfig(indicator, config) {
        const fast = config.fast || 12;
        const slow = config.slow || 26;
        const signal = config.signal || 9;
        const colors = config.colors || {
            macd: '#2196F3',
            signal: '#FF9800',
            histogram: '#4CAF50'
        };
        const lineWidth = config.lineWidth || 2;

        return `
            <div class="config-section">
                <h4>MACD Parameters</h4>
                <div class="config-row">
                    <label>Fast EMA:</label>
                    <input type="number" min="1" max="50" value="${fast}" 
                           onchange="multiIndicatorConfig.updateMACDParam('${indicator.name}', 'fast', this.value)">
                </div>
                <div class="config-row">
                    <label>Slow EMA:</label>
                    <input type="number" min="1" max="100" value="${slow}" 
                           onchange="multiIndicatorConfig.updateMACDParam('${indicator.name}', 'slow', this.value)">
                </div>
                <div class="config-row">
                    <label>Signal:</label>
                    <input type="number" min="1" max="50" value="${signal}" 
                           onchange="multiIndicatorConfig.updateMACDParam('${indicator.name}', 'signal', this.value)">
                </div>
                <h4>Colors</h4>
                <div class="config-row">
                    <label>MACD Line:</label>
                    <input type="color" value="${colors.macd}" 
                           onchange="multiIndicatorConfig.updateMACDColor('${indicator.name}', 'macd', this.value)">
                </div>
                <div class="config-row">
                    <label>Signal Line:</label>
                    <input type="color" value="${colors.signal}" 
                           onchange="multiIndicatorConfig.updateMACDColor('${indicator.name}', 'signal', this.value)">
                </div>
                <div class="config-row">
                    <label>Histogram:</label>
                    <input type="color" value="${colors.histogram}" 
                           onchange="multiIndicatorConfig.updateMACDColor('${indicator.name}', 'histogram', this.value)">
                </div>
                <div class="config-row">
                    <label>Line Width:</label>
                    <input type="range" min="1" max="5" value="${lineWidth}" 
                           onchange="multiIndicatorConfig.updateLineWidth('${indicator.name}', this.value)">
                    <span>${lineWidth}px</span>
                </div>
            </div>
        `;
    }

    renderBollingerBandsConfig(indicator, config) {
        const period = config.period || 20;
        const stdDev = config.stdDev || 2;
        const colors = config.colors || {
            upper: '#FF5722',
            middle: '#607D8B',
            lower: '#FF5722'
        };
        const fillOpacity = config.fillOpacity || 0.1;
        const lineWidth = config.lineWidth || 1;

        return `
            <div class="config-section">
                <h4>Bollinger Bands Parameters</h4>
                <div class="config-row">
                    <label>Period:</label>
                    <input type="number" min="5" max="100" value="${period}" 
                           onchange="multiIndicatorConfig.updateBBParam('${indicator.name}', 'period', this.value)">
                </div>
                <div class="config-row">
                    <label>Standard Deviation:</label>
                    <input type="number" min="0.5" max="5" step="0.1" value="${stdDev}" 
                           onchange="multiIndicatorConfig.updateBBParam('${indicator.name}', 'stdDev', this.value)">
                </div>
                <h4>Colors</h4>
                <div class="config-row">
                    <label>Upper Band:</label>
                    <input type="color" value="${colors.upper}" 
                           onchange="multiIndicatorConfig.updateBBColor('${indicator.name}', 'upper', this.value)">
                </div>
                <div class="config-row">
                    <label>Middle Band:</label>
                    <input type="color" value="${colors.middle}" 
                           onchange="multiIndicatorConfig.updateBBColor('${indicator.name}', 'middle', this.value)">
                </div>
                <div class="config-row">
                    <label>Lower Band:</label>
                    <input type="color" value="${colors.lower}" 
                           onchange="multiIndicatorConfig.updateBBColor('${indicator.name}', 'lower', this.value)">
                </div>
                <div class="config-row">
                    <label>Fill Opacity:</label>
                    <input type="range" min="0" max="1" step="0.1" value="${fillOpacity}" 
                           onchange="multiIndicatorConfig.updateBBParam('${indicator.name}', 'fillOpacity', this.value)">
                    <span>${Math.round(fillOpacity * 100)}%</span>
                </div>
                <div class="config-row">
                    <label>Line Width:</label>
                    <input type="range" min="1" max="5" value="${lineWidth}" 
                           onchange="multiIndicatorConfig.updateLineWidth('${indicator.name}', this.value)">
                    <span>${lineWidth}px</span>
                </div>
            </div>
        `;
    }

    renderStochasticConfig(indicator, config) {
        const kPeriod = config.kPeriod || 14;
        const dPeriod = config.dPeriod || 3;
        const colors = config.colors || {
            k: '#E91E63',
            d: '#9C27B0'
        };
        const overbought = config.overbought || 80;
        const oversold = config.oversold || 20;
        const lineWidth = config.lineWidth || 2;

        return `
            <div class="config-section">
                <h4>Stochastic Parameters</h4>
                <div class="config-row">
                    <label>%K Period:</label>
                    <input type="number" min="1" max="50" value="${kPeriod}" 
                           onchange="multiIndicatorConfig.updateStochParam('${indicator.name}', 'kPeriod', this.value)">
                </div>
                <div class="config-row">
                    <label>%D Period:</label>
                    <input type="number" min="1" max="20" value="${dPeriod}" 
                           onchange="multiIndicatorConfig.updateStochParam('${indicator.name}', 'dPeriod', this.value)">
                </div>
                <h4>Colors</h4>
                <div class="config-row">
                    <label>%K Line:</label>
                    <input type="color" value="${colors.k}" 
                           onchange="multiIndicatorConfig.updateStochColor('${indicator.name}', 'k', this.value)">
                </div>
                <div class="config-row">
                    <label>%D Line:</label>
                    <input type="color" value="${colors.d}" 
                           onchange="multiIndicatorConfig.updateStochColor('${indicator.name}', 'd', this.value)">
                </div>
                <div class="config-row">
                    <label>Overbought Level:</label>
                    <input type="number" min="50" max="100" value="${overbought}" 
                           onchange="multiIndicatorConfig.updateStochParam('${indicator.name}', 'overbought', this.value)">
                </div>
                <div class="config-row">
                    <label>Oversold Level:</label>
                    <input type="number" min="0" max="50" value="${oversold}" 
                           onchange="multiIndicatorConfig.updateStochParam('${indicator.name}', 'oversold', this.value)">
                </div>
                <div class="config-row">
                    <label>Line Width:</label>
                    <input type="range" min="1" max="5" value="${lineWidth}" 
                           onchange="multiIndicatorConfig.updateLineWidth('${indicator.name}', this.value)">
                    <span>${lineWidth}px</span>
                </div>
            </div>
        `;
    }

    renderGenericConfig(indicator, config) {
        const period = config.period || 20;
        const color = config.color || '#2196F3';
        const lineWidth = config.lineWidth || 2;

        return `
            <div class="config-section">
                <h4>${indicator.display_name} Settings</h4>
                <div class="config-row">
                    <label>Period:</label>
                    <input type="number" min="1" max="200" value="${period}" 
                           onchange="multiIndicatorConfig.updateGenericParam('${indicator.name}', 'period', this.value)">
                </div>
                <div class="config-row">
                    <label>Color:</label>
                    <input type="color" value="${color}" 
                           onchange="multiIndicatorConfig.updateGenericParam('${indicator.name}', 'color', this.value)">
                </div>
                <div class="config-row">
                    <label>Line Width:</label>
                    <input type="range" min="1" max="5" value="${lineWidth}" 
                           onchange="multiIndicatorConfig.updateLineWidth('${indicator.name}', this.value)">
                    <span>${lineWidth}px</span>
                </div>
            </div>
        `;
    }

    // Event handlers for configuration updates
    toggleIndicator(indicatorName, enabled) {
        if (!this.currentConfigs[indicatorName]) {
            this.currentConfigs[indicatorName] = {
                config: this.indicatorDefaults[indicatorName]?.default_config || {},
                is_enabled: enabled,
                display_order: 0
            };
        } else {
            this.currentConfigs[indicatorName].is_enabled = enabled;
        }

        this.markAsModified();
        this.updatePlotButtonState();
    }

    updatePeriod(indicatorName, index, value) {
        const config = this.getOrCreateConfig(indicatorName);
        if (!config.periods) config.periods = [];
        config.periods[index] = parseInt(value);
        this.markAsModified();
    }

    updateColor(indicatorName, index, value) {
        const config = this.getOrCreateConfig(indicatorName);
        if (!config.colors) config.colors = [];
        config.colors[index] = value;
        this.markAsModified();
    }

    addPeriod(indicatorName) {
        const config = this.getOrCreateConfig(indicatorName);
        if (!config.periods) config.periods = [];
        if (!config.colors) config.colors = [];

        config.periods.push(20);
        config.colors.push('#2196F3');

        this.renderIndicatorConfigs();
        this.markAsModified();
    }

    removePeriod(indicatorName, index) {
        const config = this.getOrCreateConfig(indicatorName);
        if (config.periods && config.periods.length > 1) {
            config.periods.splice(index, 1);
            if (config.colors) config.colors.splice(index, 1);
            this.renderIndicatorConfigs();
            this.markAsModified();
        }
    }

    updateLineWidth(indicatorName, value) {
        const config = this.getOrCreateConfig(indicatorName);
        config.lineWidth = parseInt(value);

        // Update the display
        const span = document.querySelector(`[data-indicator="${indicatorName}"] .config-row span`);
        if (span) span.textContent = `${value}px`;

        this.markAsModified();
    }

    updateOverbought(indicatorName, value) {
        const config = this.getOrCreateConfig(indicatorName);
        config.overbought = parseFloat(value);
        this.markAsModified();
    }

    updateOversold(indicatorName, value) {
        const config = this.getOrCreateConfig(indicatorName);
        config.oversold = parseFloat(value);
        this.markAsModified();
    }

    updateMACDParam(indicatorName, param, value) {
        const config = this.getOrCreateConfig(indicatorName);
        config[param] = parseInt(value);
        this.markAsModified();
    }

    updateMACDColor(indicatorName, component, value) {
        const config = this.getOrCreateConfig(indicatorName);
        if (!config.colors) config.colors = {};
        config.colors[component] = value;
        this.markAsModified();
    }

    updateBBParam(indicatorName, param, value) {
        const config = this.getOrCreateConfig(indicatorName);
        config[param] = param === 'fillOpacity' ? parseFloat(value) :
                       param === 'stdDev' ? parseFloat(value) : parseInt(value);

        // Update display for opacity
        if (param === 'fillOpacity') {
            const span = document.querySelector(`[data-indicator="${indicatorName}"] .config-row span`);
            if (span && span.textContent.includes('%')) {
                span.textContent = `${Math.round(value * 100)}%`;
            }
        }

        this.markAsModified();
    }

    updateBBColor(indicatorName, band, value) {
        const config = this.getOrCreateConfig(indicatorName);
        if (!config.colors) config.colors = {};
        config.colors[band] = value;
        this.markAsModified();
    }

    updateStochParam(indicatorName, param, value) {
        const config = this.getOrCreateConfig(indicatorName);
        config[param] = parseFloat(value);
        this.markAsModified();
    }

    updateStochColor(indicatorName, line, value) {
        const config = this.getOrCreateConfig(indicatorName);
        if (!config.colors) config.colors = {};
        config.colors[line] = value;
        this.markAsModified();
    }

    updateGenericParam(indicatorName, param, value) {
        const config = this.getOrCreateConfig(indicatorName);
        config[param] = param === 'period' ? parseInt(value) : value;
        this.markAsModified();
    }

    getOrCreateConfig(indicatorName) {
        if (!this.currentConfigs[indicatorName]) {
            this.currentConfigs[indicatorName] = {
                config: { ...this.indicatorDefaults[indicatorName]?.default_config || {} },
                is_enabled: true,
                display_order: 0
            };
        }
        return this.currentConfigs[indicatorName].config;
    }

    showMessage(message, type = 'info', duration = 3000) {
        // Create or get message container
        let messageContainer = document.getElementById('indicator-messages');
        if (!messageContainer) {
            messageContainer = document.createElement('div');
            messageContainer.id = 'indicator-messages';
            messageContainer.className = 'indicator-messages';

            const indicatorPanel = document.getElementById('indicators-panel');
            if (indicatorPanel) {
                indicatorPanel.insertBefore(messageContainer, indicatorPanel.firstChild);
            }
        }

        // Create message element
        const messageElement = document.createElement('div');
        messageElement.className = `indicator-message ${type}`;
        messageElement.innerHTML = `
            <span class="message-icon">${type === 'success' ? '✅' : type === 'error' ? '❌' : 'ℹ️'}</span>
            <span class="message-text">${message}</span>
            <button class="message-close" onclick="this.parentElement.remove()">×</button>
        `;

        messageContainer.appendChild(messageElement);

        // Auto-remove after duration
        setTimeout(() => {
            if (messageElement.parentElement) {
                messageElement.remove();
            }
        }, duration);
    }

    updateChart() {
        console.log('MultiIndicatorConfigManager: updateChart() called');
        console.log('MultiIndicatorConfigManager: window.professionalChart:', !!window.professionalChart);
        console.log('MultiIndicatorConfigManager: window.chartManager:', !!window.chartManager);
        console.log('MultiIndicatorConfigManager: this.currentStrategy:', this.currentStrategy);

        // Check for available chart instances
        const chartInstance = window.professionalChart || window.chartManager;

        // Trigger chart update with new indicators
        if (chartInstance && this.currentStrategy) {
            const enabledConfigs = {};
            Object.keys(this.currentConfigs).forEach(indicatorName => {
                const config = this.currentConfigs[indicatorName];
                if (config.is_enabled) {
                    enabledConfigs[indicatorName] = config.config;
                }
            });

            console.log('MultiIndicatorConfigManager: Enabled configs for chart:', enabledConfigs);

            // Dispatch event for chart update
            console.log('MultiIndicatorConfigManager: Dispatching indicatorsChanged event...');
            document.dispatchEvent(new CustomEvent('indicatorsChanged', {
                detail: {
                    strategyId: this.currentStrategy,
                    indicators: enabledConfigs
                }
            }));
            console.log('MultiIndicatorConfigManager: indicatorsChanged event dispatched');

            // Fallback: Try to use indicators manager directly if available
            if (window.indicatorsManager && Object.keys(enabledConfigs).length > 0) {
                console.log('MultiIndicatorConfigManager: Fallback - calling indicators manager directly');
                try {
                    // Update indicators manager configuration
                    Object.keys(enabledConfigs).forEach(indicatorName => {
                        const config = enabledConfigs[indicatorName];
                        window.indicatorsManager.updateIndicatorConfig(indicatorName, config);
                    });

                    // Trigger plotting
                    setTimeout(() => {
                        window.indicatorsManager.plotIndicators();
                    }, 100);
                } catch (error) {
                    console.error('MultiIndicatorConfigManager: Error in fallback indicator plotting:', error);
                }
            }
        } else {
            console.log('MultiIndicatorConfigManager: Cannot update chart - missing chart instance or strategy');
            console.log('MultiIndicatorConfigManager: Available chart instances:', {
                professionalChart: !!window.professionalChart,
                chartManager: !!window.chartManager,
                currentStrategy: !!this.currentStrategy
            });
        }
    }

    async plotIndicators() {
        console.log('MultiIndicatorConfigManager: plotIndicators() called');
        console.log('MultiIndicatorConfigManager: Current strategy:', this.currentStrategy);
        console.log('MultiIndicatorConfigManager: Current configs:', this.currentConfigs);

        if (!this.currentStrategy) {
            console.log('MultiIndicatorConfigManager: No strategy selected');
            this.showMessage('Please select a strategy first', 'error');
            return;
        }

        // Check if we have chart data
        console.log('MultiIndicatorConfigManager: Checking chart data...');
        console.log('MultiIndicatorConfigManager: window.professionalChart:', !!window.professionalChart);
        console.log('MultiIndicatorConfigManager: candlestickSeries:', !!(window.professionalChart && window.professionalChart.candlestickSeries));

        if (!window.professionalChart || !window.professionalChart.candlestickSeries) {
            console.log('MultiIndicatorConfigManager: No chart data available');
            this.showMessage('No chart data available. Please load data first.', 'error');
            return;
        }

        // Get enabled indicators
        const enabledIndicators = Object.keys(this.currentConfigs).filter(
            indicatorName => this.currentConfigs[indicatorName].is_enabled
        );

        console.log('MultiIndicatorConfigManager: Enabled indicators:', enabledIndicators);

        if (enabledIndicators.length === 0) {
            console.log('MultiIndicatorConfigManager: No indicators enabled');
            this.showMessage('No indicators enabled. Please enable at least one indicator.', 'warning');
            return;
        }

        try {
            console.log('MultiIndicatorConfigManager: Starting to plot indicators...');
            this.showMessage('Plotting indicators on chart...', 'info');

            // Trigger chart update
            console.log('MultiIndicatorConfigManager: Calling updateChart()...');
            this.updateChart();

            console.log('MultiIndicatorConfigManager: Successfully triggered chart update');
            this.showMessage(`Successfully plotted ${enabledIndicators.length} indicator(s)`, 'success');
        } catch (error) {
            console.error('MultiIndicatorConfigManager: Error plotting indicators:', error);
            this.showMessage('Error plotting indicators', 'error');
        }
    }

    // Enhanced method to handle indicator removal
    async removeIndicator(indicatorName) {
        if (!this.currentStrategy) {
            this.showMessage('No strategy selected', 'error');
            return;
        }

        if (!confirm(`Remove ${indicatorName} from this strategy?`)) {
            return;
        }

        try {
            const response = await fetch(`/api/v1/indicators/strategies/${this.currentStrategy}/indicators/${indicatorName}`, {
                method: 'DELETE'
            });

            const data = await response.json();

            if (data.success) {
                // Remove from current configs
                delete this.currentConfigs[indicatorName];

                // Refresh UI
                this.renderIndicatorConfigs();
                this.populateAvailableIndicators();

                this.showMessage(`${indicatorName} removed successfully`, 'success');

                // Update chart
                this.updateChart();
            } else {
                this.showMessage('Failed to remove indicator: ' + (data.message || 'Unknown error'), 'error');
            }
        } catch (error) {
            console.error('Error removing indicator:', error);
            this.showMessage('Error removing indicator', 'error');
        }
    }

    resetIndicator(indicatorName) {
        const defaults = this.indicatorDefaults[indicatorName];
        if (defaults) {
            this.currentConfigs[indicatorName] = {
                config: { ...defaults.default_config },
                is_enabled: true,
                display_order: 0
            };
            this.renderIndicatorConfigs();
            this.markAsModified();
        }
    }

    resetToDefaults() {
        if (confirm('Reset all indicators to default settings?')) {
            this.currentConfigs = {};
            this.renderIndicatorConfigs();
            this.markAsModified();
        }
    }

    async saveAllConfigurations() {
        if (!this.currentStrategy) {
            alert('Please select a strategy first');
            return;
        }

        // Validate configuration
        const errors = this.validateConfiguration();
        if (errors.length > 0) {
            alert('Configuration errors:\n' + errors.join('\n'));
            return;
        }

        try {
            console.log('MultiIndicatorConfigManager: Saving configurations for strategy:', this.currentStrategy);
            console.log('MultiIndicatorConfigManager: Configurations to save:', this.currentConfigs);

            const response = await fetch(`/api/v1/indicators/strategies/${this.currentStrategy}/indicators/bulk`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(this.currentConfigs)
            });

            console.log('MultiIndicatorConfigManager: Response status:', response.status);
            console.log('MultiIndicatorConfigManager: Response ok:', response.ok);

            if (!response.ok) {
                const errorText = await response.text();
                console.error('MultiIndicatorConfigManager: Server error response:', errorText);
                throw new Error(`Server error (${response.status}): ${errorText}`);
            }

            const data = await response.json();
            console.log('MultiIndicatorConfigManager: Response data:', data);

            if (data.success) {
                this.showMessage('Indicator configurations saved successfully!', 'success');
                this.clearModified();

                // Trigger indicator recalculation
                this.triggerIndicatorRecalculation();
            } else {
                throw new Error(data.message || 'Failed to save configurations');
            }
        } catch (error) {
            console.error('MultiIndicatorConfigManager: Error saving configurations:', error);
            this.showMessage('Error saving configurations: ' + error.message, 'error');
        }
    }

    async triggerIndicatorRecalculation() {
        // Dispatch event to trigger chart update with new indicators
        document.dispatchEvent(new CustomEvent('indicatorsConfigChanged', {
            detail: {
                strategyId: this.currentStrategy,
                configs: this.currentConfigs
            }
        }));
    }

    markAsModified() {
        const saveBtn = document.getElementById('save-indicator-config');
        if (saveBtn) {
            saveBtn.classList.add('modified');
            saveBtn.textContent = 'Save Changes *';
        }
    }

    clearModified() {
        const saveBtn = document.getElementById('save-indicator-config');
        if (saveBtn) {
            saveBtn.classList.remove('modified');
            saveBtn.textContent = 'Save Configuration';
        }
    }

    showMessage(message, type = 'info') {
        // Create or update message display
        let messageDiv = document.getElementById('indicator-config-message');
        if (!messageDiv) {
            messageDiv = document.createElement('div');
            messageDiv.id = 'indicator-config-message';
            messageDiv.className = 'config-message';

            const container = document.getElementById('indicator-config-container');
            if (container) {
                container.parentNode.insertBefore(messageDiv, container);
            }
        }

        messageDiv.className = `config-message ${type}`;
        messageDiv.textContent = message;
        messageDiv.style.display = 'block';

        // Auto-hide after 3 seconds
        setTimeout(() => {
            messageDiv.style.display = 'none';
        }, 3000);
    }

    // Get current configuration for external use
    getCurrentConfiguration() {
        return {
            strategyId: this.currentStrategy,
            indicators: this.currentConfigs
        };
    }

    // Test method for debugging
    testIndicatorPlotting() {
        console.log('=== TESTING INDICATOR PLOTTING ===');
        console.log('Current strategy:', this.currentStrategy);
        console.log('Current configs:', this.currentConfigs);
        console.log('Available chart instances:', {
            professionalChart: !!window.professionalChart,
            chartManager: !!window.chartManager,
            advancedIndicatorPlotter: !!window.advancedIndicatorPlotter
        });

        if (this.currentStrategy && Object.keys(this.currentConfigs).length > 0) {
            console.log('Attempting to trigger indicator plotting...');
            this.updateChart();
        } else {
            console.log('Cannot test - missing strategy or indicators');
        }
    }

    // Validate configuration before saving
    validateConfiguration() {
        const errors = [];

        for (const [indicatorName, config] of Object.entries(this.currentConfigs)) {
            if (!config.is_enabled) continue;

            const indicatorConfig = config.config;

            // Validate periods
            if (indicatorConfig.periods) {
                for (const period of indicatorConfig.periods) {
                    if (period < 1 || period > 500) {
                        errors.push(`${indicatorName}: Period ${period} is out of range (1-500)`);
                    }
                }
            }

            // Validate MACD parameters
            if (indicatorName === 'MACD') {
                if (indicatorConfig.slow <= indicatorConfig.fast) {
                    errors.push(`${indicatorName}: Slow period must be greater than fast period`);
                }
            }

            // Validate RSI levels
            if (indicatorName === 'RSI') {
                if (indicatorConfig.overbought <= indicatorConfig.oversold) {
                    errors.push(`${indicatorName}: Overbought level must be greater than oversold level`);
                }
            }
        }

        return errors;
    }
}

// Global instance
let multiIndicatorConfig = null;

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    multiIndicatorConfig = new MultiIndicatorConfigManager();
    window.multiIndicatorConfig = multiIndicatorConfig;
    console.log('Multi-Indicator Configuration Manager initialized');

    // Add global test function for debugging
    window.testIndicatorPlotting = () => {
        if (window.multiIndicatorConfig) {
            window.multiIndicatorConfig.testIndicatorPlotting();
        } else {
            console.log('MultiIndicatorConfig not available');
        }
    };
});
