#!/usr/bin/env python3
"""
Create sample indicator configurations for testing
"""
import requests
import json

def create_sample_indicators():
    """Create sample indicator configurations via API"""
    base_url = "http://localhost:8000"
    strategy_id = 1
    
    # Sample indicator configurations
    indicators = [
        {
            "indicator_name": "EMA",
            "config": {
                "periods": [20, 50]
            },
            "is_enabled": True,
            "display_order": 1
        },
        {
            "indicator_name": "RSI",
            "config": {
                "period": 14,
                "overbought": 70,
                "oversold": 30
            },
            "is_enabled": True,
            "display_order": 2
        },
        {
            "indicator_name": "MACD",
            "config": {
                "fast_period": 12,
                "slow_period": 26,
                "signal_period": 9
            },
            "is_enabled": True,
            "display_order": 3
        },
        {
            "indicator_name": "BOLLINGER_BANDS",
            "config": {
                "period": 20,
                "std_dev": 2
            },
            "is_enabled": True,
            "display_order": 4
        }
    ]
    
    print(f"🧪 Creating sample indicators for strategy {strategy_id}")
    print("=" * 50)
    
    for indicator in indicators:
        try:
            url = f"{base_url}/api/v1/indicators/strategies/{strategy_id}/indicators"
            
            print(f"📊 Creating {indicator['indicator_name']}...")
            print(f"   URL: {url}")
            print(f"   Config: {indicator['config']}")
            
            response = requests.post(url, json=indicator)
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    print(f"   ✅ Successfully created {indicator['indicator_name']}")
                else:
                    print(f"   ❌ API returned error: {result}")
            else:
                print(f"   ❌ HTTP {response.status_code}: {response.text}")
                
        except Exception as e:
            print(f"   ❌ Error creating {indicator['indicator_name']}: {e}")
    
    # Verify the indicators were created
    print(f"\n🔍 Verifying created indicators...")
    try:
        verify_url = f"{base_url}/api/v1/indicators/strategies/{strategy_id}/indicators"
        response = requests.get(verify_url)
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                data = result.get('data', {})
                count = len(data)
                print(f"✅ Verification successful: Found {count} indicators")
                
                for name, config in data.items():
                    print(f"   - {name}: enabled={config['is_enabled']}, config={config['config']}")
                
                print(f"\n🎯 Test the frontend now:")
                print(f"   1. Open http://localhost:8000")
                print(f"   2. Select Strategy 1")
                print(f"   3. Go to Indicators tab")
                print(f"   4. You should see {count} loaded indicators")
                
            else:
                print(f"❌ Verification failed: {result}")
        else:
            print(f"❌ Verification HTTP {response.status_code}: {response.text}")
            
    except Exception as e:
        print(f"❌ Verification error: {e}")

if __name__ == "__main__":
    create_sample_indicators()
