<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Indicator Loading</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #45a049;
        }
        .test-button.secondary {
            background: #2196F3;
        }
        .test-button.secondary:hover {
            background: #1976D2;
        }
        .log-container {
            background: #1e1e1e;
            color: #00ff00;
            padding: 15px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <h1>🧪 Indicator Loading Test Page</h1>
    
    <div class="test-container">
        <h2>Test Controls</h2>
        <button class="test-button" onclick="testApiEndpoint()">Test API Endpoint</button>
        <button class="test-button secondary" onclick="simulateStrategyChange()">Simulate Strategy Change</button>
        <button class="test-button secondary" onclick="testDirectLoad()">Test Direct Load</button>
        <button class="test-button" onclick="clearLog()">Clear Log</button>
    </div>

    <div class="test-container">
        <h2>Status</h2>
        <div id="status-display" class="status info">Ready to test...</div>
    </div>

    <div class="test-container">
        <h2>Test Log</h2>
        <div id="log-display" class="log-container">Test log will appear here...\n</div>
    </div>

    <script>
        let logElement = document.getElementById('log-display');
        let statusElement = document.getElementById('status-display');

        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            logElement.textContent += `[${timestamp}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(message);
        }

        function setStatus(message, type = 'info') {
            statusElement.textContent = message;
            statusElement.className = `status ${type}`;
        }

        function clearLog() {
            logElement.textContent = 'Log cleared...\n';
        }

        async function testApiEndpoint() {
            log('🔍 Testing API endpoint directly...');
            setStatus('Testing API endpoint...', 'info');
            
            try {
                // Test with strategy ID 1 (assuming it exists)
                const strategyId = 1;
                const url = `/api/v1/indicators/strategies/${strategyId}/indicators`;
                
                log(`📡 Fetching: ${url}`);
                const response = await fetch(url);
                
                log(`📊 Response status: ${response.status} ${response.statusText}`);
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                log(`✅ API Response received:`);
                log(JSON.stringify(data, null, 2));
                
                if (data.success) {
                    const count = Object.keys(data.data).length;
                    setStatus(`✅ Successfully loaded ${count} indicators from API`, 'success');
                    log(`🎯 Found ${count} indicator configurations`);
                } else {
                    setStatus('❌ API returned error', 'error');
                    log('❌ API returned success: false');
                }
                
            } catch (error) {
                log(`❌ API test failed: ${error.message}`);
                setStatus(`❌ API test failed: ${error.message}`, 'error');
            }
        }

        function simulateStrategyChange() {
            log('🔄 Simulating strategy change event...');
            setStatus('Simulating strategy change...', 'info');
            
            const strategyId = 1;
            log(`📋 Dispatching strategyChanged event with ID: ${strategyId}`);
            
            document.dispatchEvent(new CustomEvent('strategyChanged', {
                detail: { strategyId: strategyId }
            }));
            
            log('✅ Event dispatched successfully');
            setStatus('Strategy change event dispatched', 'success');
        }

        async function testDirectLoad() {
            log('🎯 Testing direct indicator loading...');
            setStatus('Testing direct loading...', 'info');
            
            // Simulate the MultiIndicatorConfigManager functionality
            const strategyId = 1;
            
            try {
                log(`📊 Loading indicators for strategy ${strategyId}...`);
                const response = await fetch(`/api/v1/indicators/strategies/${strategyId}/indicators`);
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                
                if (data.success) {
                    const configs = data.data;
                    const count = Object.keys(configs).length;
                    
                    log(`✅ Successfully loaded ${count} indicators:`);
                    
                    for (const [name, config] of Object.entries(configs)) {
                        log(`  - ${name}: enabled=${config.is_enabled}, config=${JSON.stringify(config.config)}`);
                    }
                    
                    setStatus(`✅ Direct load successful: ${count} indicators`, 'success');
                } else {
                    log('❌ API returned error in direct load test');
                    setStatus('❌ Direct load failed', 'error');
                }
                
            } catch (error) {
                log(`❌ Direct load test failed: ${error.message}`);
                setStatus(`❌ Direct load failed: ${error.message}`, 'error');
            }
        }

        // Initialize
        log('🚀 Test page initialized');
        log('📝 Available tests:');
        log('  1. Test API Endpoint - Direct API call test');
        log('  2. Simulate Strategy Change - Event simulation');
        log('  3. Test Direct Load - Full loading simulation');
        log('');
        log('💡 Click a test button to begin...');
    </script>
</body>
</html>
